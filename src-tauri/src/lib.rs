use tauri_plugin_sql::{Migration, MigrationKind};
use serde::{Deserialize, Serialize};
use tauri::{command, State, Manager};
use std::sync::Mutex;

// Data structures for the POS system
#[derive(Debug, Serialize, Deserialize)]
pub struct Product {
    pub id: Option<i64>,
    pub name: String,
    pub description: Option<String>,
    pub price: f64,
    pub category_id: Option<i64>,
    pub vat_rate_id: i64,
    pub barcode: Option<String>,
    pub color: String,
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Category {
    pub id: Option<i64>,
    pub name: String,
    pub color: String,
    pub icon: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CartItem {
    pub product_id: i64,
    pub name: String,
    pub price: f64,
    pub quantity: i32,
    pub vat_rate: f64,
    pub color: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Transaction {
    pub id: Option<i64>,
    pub transaction_number: String,
    pub subtotal: f64,
    pub vat_amount: f64,
    pub total: f64,
    pub items: Vec<CartItem>,
    pub payment_method: String,
}

// Application state
#[derive(Debug, Default)]
pub struct AppState {
    pub current_cart: Vec<CartItem>,
    pub till_session_id: Option<i64>,
}

// POS Commands
#[tauri::command]
async fn get_products() -> Result<Vec<Product>, String> {
    // For now, return sample data until we can properly connect to the database
    // This will be replaced with actual database queries once the frontend is working
    let sample_products = vec![
        Product {
            id: Some(1),
            name: "Coffee".to_string(),
            description: Some("Premium coffee blend".to_string()),
            price: 2.50,
            category_id: Some(1),
            vat_rate_id: 1,
            barcode: Some("123456789".to_string()),
            color: "#8B4513".to_string(),
            is_active: true,
        },
        Product {
            id: Some(2),
            name: "Tea".to_string(),
            description: Some("English breakfast tea".to_string()),
            price: 2.00,
            category_id: Some(1),
            vat_rate_id: 1,
            barcode: Some("987654321".to_string()),
            color: "#228B22".to_string(),
            is_active: true,
        },
        Product {
            id: Some(3),
            name: "Sandwich".to_string(),
            description: Some("Fresh sandwich".to_string()),
            price: 4.50,
            category_id: Some(2),
            vat_rate_id: 1,
            barcode: Some("456789123".to_string()),
            color: "#DAA520".to_string(),
            is_active: true,
        },
    ];

    Ok(sample_products)
}

#[tauri::command]
async fn get_categories() -> Result<Vec<Category>, String> {
    // Sample categories for now
    let sample_categories = vec![
        Category {
            id: Some(1),
            name: "Beverages".to_string(),
            color: "#3B82F6".to_string(),
            icon: Some("☕".to_string()),
        },
        Category {
            id: Some(2),
            name: "Food".to_string(),
            color: "#10B981".to_string(),
            icon: Some("🍽️".to_string()),
        },
        Category {
            id: Some(3),
            name: "Snacks".to_string(),
            color: "#F59E0B".to_string(),
            icon: Some("🍿".to_string()),
        },
    ];

    Ok(sample_categories)
}

#[tauri::command]
async fn add_to_cart(
    product_id: i64,
    quantity: i32,
    state: State<'_, Mutex<AppState>>,
) -> Result<Vec<CartItem>, String> {
    // Get sample products for now
    let products = get_products().await?;

    let product = products.iter().find(|p| p.id == Some(product_id))
        .ok_or("Product not found")?;

    // Update cart state
    let mut app_state = state.lock().unwrap();

    // Check if item already exists in cart
    if let Some(existing_item) = app_state.current_cart.iter_mut().find(|item| item.product_id == product_id) {
        existing_item.quantity += quantity;
    } else {
        app_state.current_cart.push(CartItem {
            product_id,
            name: product.name.clone(),
            price: product.price,
            quantity,
            vat_rate: 0.2, // 20% VAT for now
            color: product.color.clone(),
        });
    }

    Ok(app_state.current_cart.clone())
}

#[tauri::command]
async fn get_cart(state: State<'_, Mutex<AppState>>) -> Result<Vec<CartItem>, String> {
    let app_state = state.lock().unwrap();
    Ok(app_state.current_cart.clone())
}

#[tauri::command]
async fn clear_cart(state: State<'_, Mutex<AppState>>) -> Result<(), String> {
    let mut app_state = state.lock().unwrap();
    app_state.current_cart.clear();
    Ok(())
}

#[tauri::command]
async fn remove_from_cart(
    product_id: i64,
    state: State<'_, Mutex<AppState>>,
) -> Result<Vec<CartItem>, String> {
    let mut app_state = state.lock().unwrap();
    app_state.current_cart.retain(|item| item.product_id != product_id);
    Ok(app_state.current_cart.clone())
}

#[tauri::command]
async fn process_transaction(
    payment_method: String,
    state: State<'_, Mutex<AppState>>,
) -> Result<Transaction, String> {
    let mut app_state = state.lock().unwrap();

    if app_state.current_cart.is_empty() {
        return Err("Cart is empty".to_string());
    }

    // Calculate totals
    let mut subtotal = 0.0;
    let mut vat_amount = 0.0;

    for item in &app_state.current_cart {
        let line_total = item.price * item.quantity as f64;
        let line_vat = line_total * item.vat_rate / (1.0 + item.vat_rate);
        subtotal += line_total - line_vat;
        vat_amount += line_vat;
    }

    let total = subtotal + vat_amount;
    let transaction_number = format!("TXN{}", std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs());

    let transaction = Transaction {
        id: None,
        transaction_number: transaction_number.clone(),
        subtotal,
        vat_amount,
        total,
        items: app_state.current_cart.clone(),
        payment_method,
    };

    // Clear cart after successful transaction
    app_state.current_cart.clear();

    Ok(transaction)
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    let migrations = vec![
        Migration {
            version: 1,
            description: "create_initial_tables",
            sql: include_str!("../migrations/001_initial.sql"),
            kind: MigrationKind::Up,
        }
    ];

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(
            tauri_plugin_sql::Builder::default()
                .add_migrations("sqlite:pos.db", migrations)
                .build(),
        )
        .manage(Mutex::new(AppState::default()))
        .invoke_handler(tauri::generate_handler![
            greet,
            get_products,
            get_categories,
            add_to_cart,
            get_cart,
            clear_cart,
            remove_from_cart,
            process_transaction
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
