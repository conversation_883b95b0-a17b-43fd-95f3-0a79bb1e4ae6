<script lang="ts">
  import { invoke } from "@tauri-apps/api/core";
  import { onMount } from "svelte";

  // Types
  interface Product {
    id: number;
    name: string;
    description?: string;
    price: number;
    category_id?: number;
    vat_rate_id: number;
    barcode?: string;
    color: string;
    is_active: boolean;
  }

  interface Category {
    id: number;
    name: string;
    color: string;
    icon?: string;
  }

  interface CartItem {
    product_id: number;
    name: string;
    price: number;
    quantity: number;
    vat_rate: number;
    color: string;
  }

  interface Transaction {
    id?: number;
    transaction_number: string;
    subtotal: number;
    vat_amount: number;
    total: number;
    items: CartItem[];
    payment_method: string;
  }

  // State
  let products = $state<Product[]>([]);
  let categories = $state<Category[]>([]);
  let cart = $state<CartItem[]>([]);
  let selectedCategory = $state<number | null>(null);
  let showPayment = $state(false);
  let lastTransaction = $state<Transaction | null>(null);
  let showReceipt = $state(false);

  // Computed values
  let filteredProducts = $derived(
    selectedCategory
      ? products.filter(p => p.category_id === selectedCategory)
      : products
  );

  let cartTotal = $derived(
    cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  );

  let cartVAT = $derived(
    cart.reduce((vat, item) => {
      const lineTotal = item.price * item.quantity;
      return vat + (lineTotal * item.vat_rate / (1 + item.vat_rate));
    }, 0)
  );

  let cartSubtotal = $derived(cartTotal - cartVAT);

  // Load data on mount
  onMount(async () => {
    await loadProducts();
    await loadCategories();
    await loadCart();
  });

  async function loadProducts() {
    try {
      products = await invoke("get_products");
    } catch (error) {
      console.error("Failed to load products:", error);
    }
  }

  async function loadCategories() {
    try {
      categories = await invoke("get_categories");
    } catch (error) {
      console.error("Failed to load categories:", error);
    }
  }

  async function loadCart() {
    try {
      cart = await invoke("get_cart");
    } catch (error) {
      console.error("Failed to load cart:", error);
    }
  }

  async function addToCart(productId: number) {
    try {
      cart = await invoke("add_to_cart", {
        productId: productId,
        quantity: 1
      });
    } catch (error) {
      console.error("Failed to add to cart:", error);
    }
  }

  async function removeFromCart(productId: number) {
    try {
      cart = await invoke("remove_from_cart", { productId });
    } catch (error) {
      console.error("Failed to remove from cart:", error);
    }
  }

  async function clearCart() {
    try {
      await invoke("clear_cart");
      cart = [];
    } catch (error) {
      console.error("Failed to clear cart:", error);
    }
  }

  async function processPayment(paymentMethod: string) {
    try {
      lastTransaction = await invoke("process_transaction", { paymentMethod });
      cart = [];
      showPayment = false;
      showReceipt = true;
    } catch (error) {
      console.error("Failed to process payment:", error);
    }
  }

  function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  }
</script>

<!-- Main POS Interface -->
<div class="pos-container">
  <!-- Header -->
  <header class="pos-header">
    <h1 class="pos-title">🏪 UK Point of Sale</h1>
    <div class="header-actions">
      <button class="btn btn-secondary" onclick={() => clearCart()}>
        🗑️ Clear Cart
      </button>
    </div>
  </header>

  <!-- Main Content -->
  <div class="pos-main">
    <!-- Left Panel - Categories & Products -->
    <div class="products-panel">
      <!-- Categories -->
      <div class="categories-section">
        <h2 class="section-title">📂 Categories</h2>
        <div class="categories-grid">
          <button
            class="category-btn {selectedCategory === null ? 'active' : ''}"
            onclick={() => selectedCategory = null}
          >
            <span class="category-icon">🌟</span>
            <span class="category-name">All</span>
          </button>
          {#each categories as category}
            <button
              class="category-btn {selectedCategory === category.id ? 'active' : ''}"
              style="--category-color: {category.color}"
              onclick={() => selectedCategory = category.id}
            >
              <span class="category-icon">{category.icon || '📦'}</span>
              <span class="category-name">{category.name}</span>
            </button>
          {/each}
        </div>
      </div>

      <!-- Products -->
      <div class="products-section">
        <h2 class="section-title">🛍️ Products</h2>
        <div class="products-grid">
          {#each filteredProducts as product}
            <button
              class="product-card"
              style="--product-color: {product.color}"
              onclick={() => addToCart(product.id)}
            >
              <div class="product-info">
                <h3 class="product-name">{product.name}</h3>
                <p class="product-description">{product.description || ''}</p>
                <div class="product-price">{formatCurrency(product.price)}</div>
              </div>
              <div class="product-add">➕</div>
            </button>
          {/each}
        </div>
      </div>
    </div>

    <!-- Right Panel - Cart -->
    <div class="cart-panel">
      <div class="cart-header">
        <h2 class="section-title">🛒 Shopping Cart</h2>
        <div class="cart-count">{cart.length} items</div>
      </div>

      <div class="cart-items">
        {#if cart.length === 0}
          <div class="empty-cart">
            <div class="empty-icon">🛒</div>
            <p>Your cart is empty</p>
            <p class="empty-subtitle">Add some products to get started!</p>
          </div>
        {:else}
          {#each cart as item}
            <div class="cart-item" style="--item-color: {item.color}">
              <div class="item-info">
                <h4 class="item-name">{item.name}</h4>
                <div class="item-details">
                  <span class="item-price">{formatCurrency(item.price)}</span>
                  <span class="item-quantity">× {item.quantity}</span>
                </div>
              </div>
              <div class="item-actions">
                <div class="item-total">{formatCurrency(item.price * item.quantity)}</div>
                <button
                  class="btn-remove"
                  onclick={() => removeFromCart(item.product_id)}
                >
                  ❌
                </button>
              </div>
            </div>
          {/each}
        {/if}
      </div>

      <!-- Cart Summary -->
      {#if cart.length > 0}
        <div class="cart-summary">
          <div class="summary-line">
            <span>Subtotal:</span>
            <span>{formatCurrency(cartSubtotal)}</span>
          </div>
          <div class="summary-line">
            <span>VAT (20%):</span>
            <span>{formatCurrency(cartVAT)}</span>
          </div>
          <div class="summary-line total-line">
            <span>Total:</span>
            <span>{formatCurrency(cartTotal)}</span>
          </div>

          <div class="payment-buttons">
            <button
              class="btn btn-primary payment-btn"
              onclick={() => processPayment('cash')}
            >
              💷 Cash Payment
            </button>
            <button
              class="btn btn-primary payment-btn"
              onclick={() => processPayment('card')}
            >
              💳 Card Payment
            </button>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- Receipt Modal -->
{#if showReceipt && lastTransaction}
  <div class="modal-overlay" onclick={() => showReceipt = false}>
    <div class="receipt-modal" onclick={(e) => e.stopPropagation()}>
      <div class="receipt-header">
        <h2>🧾 Receipt</h2>
        <button class="btn-close" onclick={() => showReceipt = false}>✕</button>
      </div>

      <div class="receipt-content">
        <div class="receipt-business">
          <h3>Your Business Name</h3>
          <p>123 High Street, London, UK</p>
          <p>VAT: GB123456789</p>
        </div>

        <div class="receipt-transaction">
          <p><strong>Transaction:</strong> {lastTransaction.transaction_number}</p>
          <p><strong>Date:</strong> {new Date().toLocaleString('en-GB')}</p>
          <p><strong>Payment:</strong> {lastTransaction.payment_method}</p>
        </div>

        <div class="receipt-items">
          {#each lastTransaction.items as item}
            <div class="receipt-item">
              <span class="receipt-item-name">{item.name}</span>
              <span class="receipt-item-qty">× {item.quantity}</span>
              <span class="receipt-item-price">{formatCurrency(item.price * item.quantity)}</span>
            </div>
          {/each}
        </div>

        <div class="receipt-totals">
          <div class="receipt-line">
            <span>Subtotal:</span>
            <span>{formatCurrency(lastTransaction.subtotal)}</span>
          </div>
          <div class="receipt-line">
            <span>VAT:</span>
            <span>{formatCurrency(lastTransaction.vat_amount)}</span>
          </div>
          <div class="receipt-line receipt-total">
            <span><strong>Total:</strong></span>
            <span><strong>{formatCurrency(lastTransaction.total)}</strong></span>
          </div>
        </div>

        <div class="receipt-footer">
          <p>Thank you for your business!</p>
        </div>
      </div>

      <div class="receipt-actions">
        <button class="btn btn-primary" onclick={() => window.print()}>
          🖨️ Print Receipt
        </button>
        <button class="btn btn-secondary" onclick={() => showReceipt = false}>
          Close
        </button>
      </div>
    </div>
  </div>
{/if}

<style>
  /* Global Styles */
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  /* POS Container */
  .pos-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  /* Header */
  .pos-header {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .pos-title {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .header-actions {
    display: flex;
    gap: 1rem;
  }

  /* Main Content */
  .pos-main {
    flex: 1;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    padding: 2rem;
    min-height: 0;
  }

  /* Products Panel */
  .products-panel {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    overflow: hidden;
  }

  .section-title {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    font-weight: bold;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  /* Categories */
  .categories-section {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
  }

  .category-btn {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    border: none;
    border-radius: 12px;
    padding: 1rem;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
  }

  .category-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
  }

  .category-btn.active {
    background: linear-gradient(135deg, #00b894, #00a085);
    box-shadow: 0 4px 15px rgba(0, 184, 148, 0.4);
  }

  .category-btn[style*="--category-color"] {
    background: linear-gradient(135deg, var(--category-color), color-mix(in srgb, var(--category-color) 80%, black));
  }

  .category-icon {
    font-size: 1.5rem;
  }

  .category-name {
    font-size: 0.9rem;
  }

  /* Products */
  .products-section {
    flex: 1;
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
    overflow-y: auto;
    flex: 1;
    padding-right: 0.5rem;
  }

  .product-card {
    background: linear-gradient(135deg, #a29bfe, #6c5ce7);
    border: none;
    border-radius: 16px;
    padding: 1.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 140px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(162, 155, 254, 0.3);
  }

  .product-card:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 30px rgba(162, 155, 254, 0.4);
  }

  .product-card[style*="--product-color"] {
    background: linear-gradient(135deg, var(--product-color), color-mix(in srgb, var(--product-color) 70%, black));
  }

  .product-info {
    flex: 1;
  }

  .product-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: bold;
  }

  .product-description {
    margin: 0 0 1rem 0;
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.3;
  }

  .product-price {
    font-size: 1.3rem;
    font-weight: bold;
    color: #fff;
  }

  .product-add {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.8;
    transition: all 0.3s ease;
  }

  .product-card:hover .product-add {
    opacity: 1;
    transform: scale(1.2);
  }

  /* Cart Panel */
  .cart-panel {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 8rem);
  }

  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
  }

  .cart-count {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
  }

  .cart-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1rem;
  }

  .empty-cart {
    text-align: center;
    padding: 3rem 1rem;
    color: #64748b;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-subtitle {
    font-size: 0.9rem;
    opacity: 0.7;
  }

  .cart-item {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 4px solid var(--item-color, #3b82f6);
    transition: all 0.3s ease;
  }

  .cart-item:hover {
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .item-info {
    flex: 1;
  }

  .item-name {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: bold;
    color: #1e293b;
  }

  .item-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #64748b;
  }

  .item-price {
    font-weight: 600;
  }

  .item-quantity {
    color: #059669;
    font-weight: bold;
  }

  .item-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }

  .item-total {
    font-size: 1.1rem;
    font-weight: bold;
    color: #059669;
  }

  .btn-remove {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    border-radius: 8px;
    padding: 0.25rem 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
  }

  .btn-remove:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
  }

  /* Cart Summary */
  .cart-summary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #e2e8f0;
  }

  .summary-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 1rem;
  }

  .total-line {
    font-size: 1.2rem;
    font-weight: bold;
    color: #059669;
    border-top: 2px solid #d1d5db;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
  }

  .payment-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .payment-btn {
    padding: 1rem;
    font-size: 1rem;
    font-weight: bold;
  }

  /* Buttons */
  .btn {
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
  }

  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .receipt-modal {
    background: white;
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .receipt-header {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .receipt-header h2 {
    margin: 0;
    font-size: 1.5rem;
  }

  .btn-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.5rem;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
  }

  .btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  .receipt-content {
    padding: 2rem;
  }

  .receipt-business {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px dashed #d1d5db;
  }

  .receipt-business h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    color: #1e293b;
  }

  .receipt-business p {
    margin: 0.25rem 0;
    color: #64748b;
  }

  .receipt-transaction {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .receipt-transaction p {
    margin: 0.5rem 0;
    color: #374151;
  }

  .receipt-items {
    margin-bottom: 1.5rem;
  }

  .receipt-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
  }

  .receipt-item-name {
    font-weight: 600;
    color: #1e293b;
  }

  .receipt-item-qty {
    color: #059669;
    font-weight: bold;
  }

  .receipt-item-price {
    font-weight: bold;
    color: #1e293b;
  }

  .receipt-totals {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .receipt-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }

  .receipt-total {
    font-size: 1.2rem;
    border-top: 2px solid #d1d5db;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
  }

  .receipt-footer {
    text-align: center;
    color: #64748b;
    font-style: italic;
  }

  .receipt-actions {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
  }

  .receipt-actions .btn {
    flex: 1;
  }

  /* Responsive Design */
  @media (max-width: 1024px) {
    .pos-main {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .categories-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }

    .products-grid {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .pos-header {
      padding: 1rem;
      flex-direction: column;
      gap: 1rem;
    }

    .pos-title {
      font-size: 1.5rem;
    }

    .payment-buttons {
      grid-template-columns: 1fr;
    }
  }
</style>
