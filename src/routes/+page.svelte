<script lang="ts">
  import { onMount } from "svelte";

  // Dashboard state
  let currentTime = $state(new Date());
  let businessStats = $state({
    todaySales: 1247.50,
    todayTransactions: 89,
    tillStatus: 'open',
    lastBackup: '2024-01-15 09:30'
  });

  // Update time every second
  onMount(() => {
    const interval = setInterval(() => {
      currentTime = new Date();
    }, 1000);

    return () => clearInterval(interval);
  });

  function formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-GB', {
      style: 'currency',
      currency: 'GBP'
    }).format(amount);
  }

  function formatTime(date: Date): string {
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  function formatDate(date: Date): string {
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
</script>

<!-- Management Dashboard -->
<div class="dashboard-container">
  <!-- Header -->
  <header class="dashboard-header">
    <div class="header-left">
      <h1 class="dashboard-title">🏪 UK POS Management Dashboard</h1>
      <div class="business-info">
        <span class="business-name">Your Business Name</span>
        <span class="business-location">📍 London, UK</span>
      </div>
    </div>
    <div class="header-right">
      <div class="current-time">
        <div class="time">{formatTime(currentTime)}</div>
        <div class="date">{formatDate(currentTime)}</div>
      </div>
      <div class="till-status">
        <span class="status-indicator {businessStats.tillStatus}"></span>
        <span>Till {businessStats.tillStatus === 'open' ? 'Open' : 'Closed'}</span>
      </div>
    </div>
  </header>

  <!-- Quick Stats Bar -->
  <div class="stats-bar">
    <div class="stat-card">
      <div class="stat-icon">💰</div>
      <div class="stat-content">
        <div class="stat-value">{formatCurrency(businessStats.todaySales)}</div>
        <div class="stat-label">Today's Sales</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">🧾</div>
      <div class="stat-content">
        <div class="stat-value">{businessStats.todayTransactions}</div>
        <div class="stat-label">Transactions</div>
      </div>
    </div>
    <div class="stat-card">
      <div class="stat-icon">💾</div>
      <div class="stat-content">
        <div class="stat-value">{businessStats.lastBackup}</div>
        <div class="stat-label">Last Backup</div>
      </div>
    </div>
  </div>

  <!-- Main Dashboard Grid -->
  <div class="dashboard-grid">
    <!-- POS Operations -->
    <div class="tile-section">
      <h2 class="section-title">🛒 POS Operations</h2>
      <div class="tiles-grid">
        <a href="/pos" class="dashboard-tile primary">
          <div class="tile-icon">🏪</div>
          <div class="tile-content">
            <h3>Point of Sale</h3>
            <p>Process transactions and manage sales</p>
          </div>
          <div class="tile-arrow">→</div>
        </a>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">💷</div>
          <div class="tile-content">
            <h3>Open Till</h3>
            <p>Start a new till session</p>
          </div>
        </button>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">🔒</div>
          <div class="tile-content">
            <h3>Close Till</h3>
            <p>End current session & reconcile</p>
          </div>
        </button>

        <button class="dashboard-tile accent">
          <div class="tile-icon">🚨</div>
          <div class="tile-content">
            <h3>Emergency</h3>
            <p>Emergency functions & void</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Product Management -->
    <div class="tile-section">
      <h2 class="section-title">📦 Product Management</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile primary">
          <div class="tile-icon">🛍️</div>
          <div class="tile-content">
            <h3>Manage Products</h3>
            <p>Add, edit, delete products</p>
          </div>
        </button>

        <button class="dashboard-tile primary">
          <div class="tile-icon">📂</div>
          <div class="tile-content">
            <h3>Categories</h3>
            <p>Organize product categories</p>
          </div>
        </button>

        <button class="dashboard-tile primary">
          <div class="tile-icon">🏷️</div>
          <div class="tile-content">
            <h3>Pricing</h3>
            <p>Update prices & promotions</p>
          </div>
        </button>

        <button class="dashboard-tile primary">
          <div class="tile-icon">🎨</div>
          <div class="tile-content">
            <h3>Product Colors</h3>
            <p>Customize product appearance</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Store Management -->
    <div class="tile-section">
      <h2 class="section-title">🏬 Store Management</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile secondary">
          <div class="tile-icon">⚙️</div>
          <div class="tile-content">
            <h3>Settings</h3>
            <p>Business details & preferences</p>
          </div>
        </button>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">💳</div>
          <div class="tile-content">
            <h3>Payment Methods</h3>
            <p>Configure payment options</p>
          </div>
        </button>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">🕒</div>
          <div class="tile-content">
            <h3>Business Hours</h3>
            <p>Set opening times</p>
          </div>
        </button>

        <button class="dashboard-tile secondary">
          <div class="tile-icon">📍</div>
          <div class="tile-content">
            <h3>Location Settings</h3>
            <p>Store location & details</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Reports & Analytics -->
    <div class="tile-section">
      <h2 class="section-title">📊 Reports & Analytics</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile accent">
          <div class="tile-icon">📈</div>
          <div class="tile-content">
            <h3>Sales Reports</h3>
            <p>Daily, weekly, monthly sales</p>
          </div>
        </button>

        <button class="dashboard-tile accent">
          <div class="tile-icon">🧾</div>
          <div class="tile-content">
            <h3>Transaction History</h3>
            <p>View all transactions</p>
          </div>
        </button>

        <button class="dashboard-tile accent">
          <div class="tile-icon">🏛️</div>
          <div class="tile-content">
            <h3>VAT Reports</h3>
            <p>UK VAT compliance reports</p>
          </div>
        </button>

        <button class="dashboard-tile accent">
          <div class="tile-icon">📋</div>
          <div class="tile-content">
            <h3>Summary Reports</h3>
            <p>Business performance overview</p>
          </div>
        </button>
      </div>
    </div>

    <!-- User Management -->
    <div class="tile-section">
      <h2 class="section-title">👥 User Management</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile warning">
          <div class="tile-icon">👤</div>
          <div class="tile-content">
            <h3>Staff Accounts</h3>
            <p>Manage user accounts</p>
          </div>
        </button>

        <button class="dashboard-tile warning">
          <div class="tile-icon">🔐</div>
          <div class="tile-content">
            <h3>Permissions</h3>
            <p>Set access levels</p>
          </div>
        </button>

        <button class="dashboard-tile warning">
          <div class="tile-icon">🔑</div>
          <div class="tile-content">
            <h3>Access Control</h3>
            <p>Security settings</p>
          </div>
        </button>

        <button class="dashboard-tile warning">
          <div class="tile-icon">📝</div>
          <div class="tile-content">
            <h3>Activity Log</h3>
            <p>User activity tracking</p>
          </div>
        </button>
      </div>
    </div>

    <!-- Promotions & Offers -->
    <div class="tile-section">
      <h2 class="section-title">🎯 Promotions & Offers</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile success">
          <div class="tile-icon">🏷️</div>
          <div class="tile-content">
            <h3>Discounts</h3>
            <p>Manage discount campaigns</p>
          </div>
        </button>

        <button class="dashboard-tile success">
          <div class="tile-icon">💸</div>
          <div class="tile-content">
            <h3>Special Pricing</h3>
            <p>Promotional pricing rules</p>
          </div>
        </button>

        <button class="dashboard-tile success">
          <div class="tile-icon">🎪</div>
          <div class="tile-content">
            <h3>Campaigns</h3>
            <p>Marketing campaigns</p>
          </div>
        </button>

        <button class="dashboard-tile success">
          <div class="tile-icon">🎁</div>
          <div class="tile-content">
            <h3>Loyalty Program</h3>
            <p>Customer loyalty rewards</p>
          </div>
        </button>
      </div>
    </div>

    <!-- System & Hardware -->
    <div class="tile-section">
      <h2 class="section-title">🔧 System & Hardware</h2>
      <div class="tiles-grid">
        <button class="dashboard-tile info">
          <div class="tile-icon">⌨️</div>
          <div class="tile-content">
            <h3>Hotkeys Config</h3>
            <p>Customize keyboard shortcuts</p>
          </div>
        </button>

        <button class="dashboard-tile info">
          <div class="tile-icon">💾</div>
          <div class="tile-content">
            <h3>System Backup</h3>
            <p>Data export & maintenance</p>
          </div>
        </button>

        <button class="dashboard-tile info">
          <div class="tile-icon">🖨️</div>
          <div class="tile-content">
            <h3>Receipt Printer</h3>
            <p>Printer settings & config</p>
          </div>
        </button>

        <button class="dashboard-tile info">
          <div class="tile-icon">📱</div>
          <div class="tile-content">
            <h3>Barcode Scanner</h3>
            <p>Scanner configuration</p>
          </div>
        </button>
      </div>
    </div>
  </div>
</div>

<style>
  /* Global Styles */
  :global(body) {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
  }

  /* Dashboard Container */
  .dashboard-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  /* Dashboard Header */
  .dashboard-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  }

  .header-left {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .dashboard-title {
    margin: 0;
    font-size: 2.2rem;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  }

  .business-info {
    display: flex;
    gap: 1rem;
    font-size: 1rem;
    opacity: 0.9;
  }

  .business-name {
    font-weight: 600;
  }

  .business-location {
    opacity: 0.8;
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 2rem;
  }

  .current-time {
    text-align: right;
  }

  .time {
    font-size: 2rem;
    font-weight: bold;
    font-family: 'Courier New', monospace;
  }

  .date {
    font-size: 0.9rem;
    opacity: 0.9;
  }

  .till-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1rem;
    border-radius: 12px;
    font-weight: bold;
  }

  .status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #ef4444;
  }

  .status-indicator.open {
    background: #10b981;
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
  }

  /* Stats Bar */
  .stats-bar {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    padding: 1.5rem 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .stat-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
  }

  .stat-content {
    flex: 1;
  }

  .stat-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #1e293b;
    margin-bottom: 0.25rem;
  }

  .stat-label {
    font-size: 0.9rem;
    color: #64748b;
    font-weight: 500;
  }

  /* Dashboard Grid */
  .dashboard-grid {
    flex: 1;
    padding: 2rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    align-items: start;
  }

  .tile-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .section-title {
    margin: 0 0 1.5rem 0;
    font-size: 1.4rem;
    font-weight: bold;
    color: #1e293b;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .tiles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
  }

  /* Dashboard Tiles */
  .dashboard-tile {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
    text-decoration: none;
    color: inherit;
    position: relative;
    overflow: hidden;
  }

  .dashboard-tile:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
  }

  .dashboard-tile.primary {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border-color: #3b82f6;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  }

  .dashboard-tile.primary:hover {
    box-shadow: 0 12px 30px rgba(59, 130, 246, 0.4);
  }

  .dashboard-tile.secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    border-color: #6b7280;
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
  }

  .dashboard-tile.secondary:hover {
    box-shadow: 0 12px 30px rgba(107, 114, 128, 0.4);
  }

  .dashboard-tile.accent {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border-color: #8b5cf6;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  }

  .dashboard-tile.accent:hover {
    box-shadow: 0 12px 30px rgba(139, 92, 246, 0.4);
  }

  .dashboard-tile.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
    border-color: #f59e0b;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
  }

  .dashboard-tile.warning:hover {
    box-shadow: 0 12px 30px rgba(245, 158, 11, 0.4);
  }

  .dashboard-tile.success {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border-color: #10b981;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  }

  .dashboard-tile.success:hover {
    box-shadow: 0 12px 30px rgba(16, 185, 129, 0.4);
  }

  .dashboard-tile.info {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
    border-color: #06b6d4;
    box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
  }

  .dashboard-tile.info:hover {
    box-shadow: 0 12px 30px rgba(6, 182, 212, 0.4);
  }

  .tile-icon {
    font-size: 2.5rem;
    opacity: 0.9;
  }

  .tile-content {
    flex: 1;
  }

  .tile-content h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    font-weight: bold;
  }

  .tile-content p {
    margin: 0;
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.3;
  }

  .tile-arrow {
    position: absolute;
    top: 1rem;
    right: 1rem;
    font-size: 1.5rem;
    opacity: 0.7;
    transition: all 0.3s ease;
  }

  .dashboard-tile:hover .tile-arrow {
    opacity: 1;
    transform: translateX(4px);
  }

  /* Responsive Design */
  @media (max-width: 1200px) {
    .dashboard-grid {
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }

    .tiles-grid {
      grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    }
  }

  @media (max-width: 768px) {
    .dashboard-header {
      padding: 1rem;
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }

    .dashboard-title {
      font-size: 1.8rem;
    }

    .header-right {
      flex-direction: column;
      gap: 1rem;
    }

    .stats-bar {
      grid-template-columns: 1fr;
      padding: 1rem;
    }

    .dashboard-grid {
      grid-template-columns: 1fr;
      padding: 1rem;
      gap: 1rem;
    }

    .tile-section {
      padding: 1.5rem;
    }

    .tiles-grid {
      grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }

    .dashboard-tile {
      padding: 1rem;
    }

    .tile-icon {
      font-size: 2rem;
    }

    .tile-content h3 {
      font-size: 1rem;
    }

    .tile-content p {
      font-size: 0.8rem;
    }
  }

  @media (max-width: 480px) {
    .dashboard-title {
      font-size: 1.5rem;
    }

    .business-info {
      flex-direction: column;
      gap: 0.5rem;
    }

    .time {
      font-size: 1.5rem;
    }

    .tiles-grid {
      grid-template-columns: 1fr;
    }

    .dashboard-tile {
      padding: 1.5rem 1rem;
    }
  }

  /* Cart Panel */
  .cart-panel {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    flex-direction: column;
    max-height: calc(100vh - 8rem);
  }

  .cart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #f1f5f9;
  }

  .cart-count {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: bold;
    font-size: 0.9rem;
  }

  .cart-items {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 1rem;
  }

  .empty-cart {
    text-align: center;
    padding: 3rem 1rem;
    color: #64748b;
  }

  .empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
  }

  .empty-subtitle {
    font-size: 0.9rem;
    opacity: 0.7;
  }

  .cart-item {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 4px solid var(--item-color, #3b82f6);
    transition: all 0.3s ease;
  }

  .cart-item:hover {
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }

  .item-info {
    flex: 1;
  }

  .item-name {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: bold;
    color: #1e293b;
  }

  .item-details {
    display: flex;
    gap: 1rem;
    font-size: 0.9rem;
    color: #64748b;
  }

  .item-price {
    font-weight: 600;
  }

  .item-quantity {
    color: #059669;
    font-weight: bold;
  }

  .item-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
  }

  .item-total {
    font-size: 1.1rem;
    font-weight: bold;
    color: #059669;
  }

  .btn-remove {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    border-radius: 8px;
    padding: 0.25rem 0.5rem;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
  }

  .btn-remove:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
  }

  /* Cart Summary */
  .cart-summary {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 12px;
    padding: 1.5rem;
    border: 2px solid #e2e8f0;
  }

  .summary-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 1rem;
  }

  .total-line {
    font-size: 1.2rem;
    font-weight: bold;
    color: #059669;
    border-top: 2px solid #d1d5db;
    padding-top: 0.75rem;
    margin-top: 0.75rem;
  }

  .payment-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .payment-btn {
    padding: 1rem;
    font-size: 1rem;
    font-weight: bold;
  }

  /* Buttons */
  .btn {
    border: none;
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
  }

  .btn-primary {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
  }

  .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  }

  .btn-secondary {
    background: linear-gradient(135deg, #6b7280, #4b5563);
    color: white;
    box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
  }

  /* Modal */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
  }

  .receipt-modal {
    background: white;
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  }

  .receipt-header {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 1.5rem;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .receipt-header h2 {
    margin: 0;
    font-size: 1.5rem;
  }

  .btn-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 8px;
    color: white;
    padding: 0.5rem;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
  }

  .btn-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
  }

  .receipt-content {
    padding: 2rem;
  }

  .receipt-business {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px dashed #d1d5db;
  }

  .receipt-business h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.3rem;
    color: #1e293b;
  }

  .receipt-business p {
    margin: 0.25rem 0;
    color: #64748b;
  }

  .receipt-transaction {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .receipt-transaction p {
    margin: 0.5rem 0;
    color: #374151;
  }

  .receipt-items {
    margin-bottom: 1.5rem;
  }

  .receipt-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f1f5f9;
  }

  .receipt-item-name {
    font-weight: 600;
    color: #1e293b;
  }

  .receipt-item-qty {
    color: #059669;
    font-weight: bold;
  }

  .receipt-item-price {
    font-weight: bold;
    color: #1e293b;
  }

  .receipt-totals {
    background: #f8fafc;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  .receipt-line {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
  }

  .receipt-total {
    font-size: 1.2rem;
    border-top: 2px solid #d1d5db;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
  }

  .receipt-footer {
    text-align: center;
    color: #64748b;
    font-style: italic;
  }

  .receipt-actions {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
  }

  .receipt-actions .btn {
    flex: 1;
  }
</style>
